<script setup lang="ts">
import type { PropType } from 'vue'
import { useNaiveForm } from '@sa/hooks'

defineOptions({
  name: 'KnowledgePointForm',
})

const modelInfo = defineModel('modelInfo', {
  type: Object as PropType<{
    KnowledgePointIds: string[]
    AdditionalRequirements: string
  }>,
  default: () => ({
    KnowledgePointIds: [],
    AdditionalRequirements: '',
  }),
})

// 表单引用和校验
const { formRef, validate } = useNaiveForm()

// 表单校验规则
const rules = {
  KnowledgePointIds: [
    {
      required: true,
      message: '请选择知识点',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: any[]) => {
        if (!value || value.length === 0) {
          return new Error('请选择知识点')
        }
        return true
      },
    },
  ],
}

// 暴露校验方法给父组件
defineExpose({
  validate,
})

// 选择知识点
function selectKnowledgePoints() {
  // TODO: 实现知识点选择逻辑
}
</script>

<template>
  <div class="p-12px">
    <NForm ref="formRef" :model="modelInfo" :rules="rules">
      <!-- 出题范围标题 -->
      <div class="mb-16px flex items-center">
        <SvgIcon icon="mdi:file-document-outline" class="mr-8px text-20px text-blue-500" />
        <span class="text-14px text-[#333] font-500">出题范围</span>
      </div>

      <!-- 知识点选择区域 -->
      <NFormItem path="KnowledgePointIds">
        <div
          class="mb-24px min-h-200px w-full flex flex-col cursor-pointer items-center justify-center border-2 border-gray-300 rounded-8px border-dashed bg-gray-50 p-24px transition-all duration-200 hover:border-blue-400 hover:bg-blue-50"
          @click="selectKnowledgePoints"
        >
          <div class="h-full flex flex-col items-center justify-center">
            <!-- 加号图标 -->
            <div class="mb-12px h-48px w-48px flex items-center justify-center rounded-full bg-blue-500 text-white">
              <SvgIcon icon="mdi:plus" class="text-24px" />
            </div>
            <!-- 提示文字 -->
            <span class="text-14px text-gray-600 font-500">选择本课知识点</span>
          </div>
        </div>
      </NFormItem>

      <!-- 补充内容区域 -->
      <div class="border border-gray-200 rounded-8px bg-white">
        <!-- 标题栏 -->
        <div
          class="flex items-center justify-between p-16px"
        >
          <span class="text-14px text-[#464646] font-500">补充内容（选填）</span>
        </div>

        <!-- 可折叠内容 -->
        <div
          class="overflow-hidden transition-all duration-300"
        >
          <div class="border-t border-gray-200 p-16px">
            <NInput
              v-model:value="modelInfo.AdditionalRequirements"
              type="textarea"
              placeholder="您可以补充特殊的出题要求。&#10;示例如下：&#10;题目中需要包含&quot;折射&quot;&quot;反射&quot;这两个词汇"
              :rows="4"
              clearable
              maxlength="500"
              show-count
              class="w-full"
            />
          </div>
        </div>
      </div>
    </NForm>
  </div>
</template>
